"""
Main NFT Bot module
"""
import asyncio
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes
from loguru import logger
from config import settings
from fragment_api import fragment_client
from database import db_manager
from mtproto_client import MTProtoClient


class NFTBot:
    """Main NFT Bot class"""
    
    def __init__(self):
        self.app = Application.builder().token(settings.bot_token).build()
        self.mtproto_client = MTProtoClient()
        self.setup_handlers()
    
    def setup_handlers(self):
        """Setup bot command and callback handlers"""
        self.app.add_handler(CommandHandler("start", self.start_command))
        self.app.add_handler(CommandHandler("collections", self.collections_command))
        self.app.add_handler(CommandHandler("casino", self.casino_command))
        self.app.add_handler(CommandHandler("inventory", self.inventory_command))
        self.app.add_handler(CommandH<PERSON><PERSON>("refresh", self.refresh_command))
        self.app.add_handler(Callback<PERSON>uery<PERSON>and<PERSON>(self.button_callback))
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        welcome_text = """
🎰 **Добро пожаловать в NFT Casino Bot!**

Этот бот позволяет:
• 🎮 Играть в казино с NFT-ставками
• 💎 Просматривать доступные NFT коллекции
• 📊 Отслеживать цены на Fragment
• 🎁 Управлять вашими NFT-подарками

Команды:
/collections - Посмотреть доступные коллекции
/casino - Начать игру в казино
/inventory - Ваши NFT-подарки
/refresh - Обновить данные о коллекциях
        """
        
        keyboard = [
            [InlineKeyboardButton("🎮 Казино", callback_data="casino")],
            [InlineKeyboardButton("💎 Коллекции", callback_data="collections")],
            [InlineKeyboardButton("🎁 Инвентарь", callback_data="inventory")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(welcome_text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def collections_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /collections command"""
        await self.show_collections(update, context)
    
    async def casino_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /casino command"""
        await self.show_casino_menu(update, context)
    
    async def inventory_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /inventory command"""
        await self.show_user_inventory(update, context)
    
    async def refresh_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /refresh command - update collection data"""
        if update.effective_user.id != settings.admin_user_id:
            await update.message.reply_text("❌ Только администратор может обновлять данные")
            return
        
        await update.message.reply_text("🔄 Обновляю данные коллекций...")
        
        # Update astralshard collection
        success = await self.update_collection_data("astralshard")
        
        if success:
            await update.message.reply_text("✅ Данные коллекций обновлены!")
        else:
            await update.message.reply_text("❌ Ошибка при обновлении данных")
    
    async def show_collections(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show available NFT collections"""
        # Get cheapest NFT from astralshard collection
        nft_data = fragment_client.fetch_cheapest_nft("astralshard")
        
        if not nft_data:
            text = "❌ Нет доступных NFT в коллекции astralshard"
        else:
            text = f"""
💎 **Доступные NFT коллекции:**

**Astral Shard Collection**
🏷️ Название: {nft_data['name']}
💰 Минимальная цена: ${nft_data['price_usd']:,.2f}
🔗 Contract Hash: `{nft_data['contract_hash'][:20]}...`
🎨 [Превью]({nft_data['preview_url']})

Definition ID: `{nft_data['definition_id']}`
            """
        
        keyboard = [
            [InlineKeyboardButton("🎮 Играть с этим NFT", callback_data=f"play_astralshard_{nft_data['definition_id'] if nft_data else '0'}")],
            [InlineKeyboardButton("🔄 Обновить", callback_data="refresh_collections")],
            [InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if update.callback_query:
            await update.callback_query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')
        else:
            await update.message.reply_text(text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def show_casino_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show casino game menu"""
        text = """
🎰 **NFT Casino**

Выберите игру:
• 🎲 Dice Game - Угадайте число (1-6)
• 🃏 Card Game - Больше/меньше
• 🎯 Roulette - Красное/черное

Ставки принимаются в NFT!
        """
        
        keyboard = [
            [InlineKeyboardButton("🎲 Dice Game", callback_data="game_dice")],
            [InlineKeyboardButton("🃏 Card Game", callback_data="game_cards")],
            [InlineKeyboardButton("🎯 Roulette", callback_data="game_roulette")],
            [InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if update.callback_query:
            await update.callback_query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')
        else:
            await update.message.reply_text(text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def show_user_inventory(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show user's NFT inventory"""
        user_id = update.effective_user.id
        
        # This would fetch from database in real implementation
        text = f"""
🎁 **Ваш NFT инвентарь**

User ID: {user_id}

📦 У вас пока нет NFT-подарков
💡 Купите NFT через казино или получите в подарок!
        """
        
        keyboard = [
            [InlineKeyboardButton("🎮 Играть в казино", callback_data="casino")],
            [InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        if update.callback_query:
            await update.callback_query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')
        else:
            await update.message.reply_text(text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle button callbacks"""
        query = update.callback_query
        await query.answer()
        
        data = query.data
        
        if data == "main_menu":
            await self.start_command(update, context)
        elif data == "collections":
            await self.show_collections(update, context)
        elif data == "casino":
            await self.show_casino_menu(update, context)
        elif data == "inventory":
            await self.show_user_inventory(update, context)
        elif data == "refresh_collections":
            await self.refresh_collections(update, context)
        elif data.startswith("play_astralshard_"):
            definition_id = data.split("_")[-1]
            await self.start_nft_game(update, context, definition_id)
        elif data.startswith("game_"):
            game_type = data.split("_")[1]
            await self.start_game(update, context, game_type)
    
    async def refresh_collections(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Refresh collection data"""
        await update.callback_query.edit_message_text("🔄 Обновляю данные...")
        
        success = await self.update_collection_data("astralshard")
        
        if success:
            await self.show_collections(update, context)
        else:
            await update.callback_query.edit_message_text("❌ Ошибка при обновлении данных")
    
    async def update_collection_data(self, collection_slug: str) -> bool:
        """Update collection data in database"""
        try:
            nft_data = fragment_client.fetch_cheapest_nft(collection_slug)
            if nft_data:
                return db_manager.add_or_update_nft_item(nft_data)
            return False
        except Exception as e:
            logger.error(f"Error updating collection data: {e}")
            return False
    
    async def start_nft_game(self, update: Update, context: ContextTypes.DEFAULT_TYPE, definition_id: str):
        """Start NFT-based game"""
        text = f"""
🎮 **Начинаем игру с NFT!**

Definition ID: `{definition_id}`

⚠️ **Внимание:** Это демо-версия
В реальной версии здесь будет:
1. Покупка NFT через MTProto
2. Игровая механика
3. Выплата выигрыша NFT

🔧 Функция в разработке...
        """
        
        keyboard = [
            [InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.callback_query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def start_game(self, update: Update, context: ContextTypes.DEFAULT_TYPE, game_type: str):
        """Start specific game type"""
        text = f"""
🎮 **{game_type.title()} Game**

🔧 Игра в разработке...

В реальной версии здесь будет полноценная игровая механика!
        """
        
        keyboard = [
            [InlineKeyboardButton("🎰 Назад к казино", callback_data="casino")],
            [InlineKeyboardButton("🏠 Главное меню", callback_data="main_menu")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.callback_query.edit_message_text(text, reply_markup=reply_markup, parse_mode='Markdown')
    
    async def run(self):
        """Run the bot"""
        logger.info("Starting NFT Bot...")
        await self.app.run_polling()


if __name__ == "__main__":
    bot = NFTBot()
    asyncio.run(bot.run())
