"""
Database models and operations for NFT Bot
"""
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, Text, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, List, Dict, Any
from loguru import logger
from config import settings

Base = declarative_base()


class NFTCollection(Base):
    """NFT Collection model"""
    __tablename__ = "nft_collections"
    
    id = Column(Integer, primary_key=True, index=True)
    slug = Column(String(100), unique=True, index=True, nullable=False)
    name = Column(String(200), nullable=False)
    description = Column(Text)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class NFTItem(Base):
    """NFT Item model"""
    __tablename__ = "nft_items"
    
    id = Column(Integer, primary_key=True, index=True)
    definition_id = Column(String(50), unique=True, index=True, nullable=False)
    token_id = Column(String(100), nullable=False)
    collection_slug = Column(String(100), nullable=False)
    name = Column(String(200), nullable=False)
    price_usd = Column(Float, nullable=False)
    price_native = Column(Float)
    contract_hash = Column(String(200))
    preview_url = Column(Text)
    external_link = Column(Text)
    attributes = Column(JSON)
    is_available = Column(Boolean, default=True)
    last_updated = Column(DateTime, default=func.now(), onupdate=func.now())
    created_at = Column(DateTime, default=func.now())


class UserGift(Base):
    """User received gifts model"""
    __tablename__ = "user_gifts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    received_gift_id = Column(String(100), nullable=False)
    definition_id = Column(String(50), nullable=False)
    gift_name = Column(String(200))
    received_at = Column(DateTime, default=func.now())
    is_used = Column(Boolean, default=False)
    used_at = Column(DateTime)


class GameSession(Base):
    """Game session model"""
    __tablename__ = "game_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    session_type = Column(String(50), nullable=False)  # 'casino', 'lottery', etc.
    bet_amount_usd = Column(Float)
    nft_definition_id = Column(String(50))
    result = Column(String(20))  # 'win', 'lose', 'pending'
    payout_amount_usd = Column(Float)
    created_at = Column(DateTime, default=func.now())
    completed_at = Column(DateTime)


class DatabaseManager:
    """Database manager class"""
    
    def __init__(self, database_url: str = None):
        self.database_url = database_url or settings.database_url
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # Create tables
        Base.metadata.create_all(bind=self.engine)
        logger.info("Database initialized successfully")
    
    def get_session(self) -> Session:
        """Get database session"""
        return self.SessionLocal()
    
    def add_or_update_nft_item(self, nft_data: Dict[str, Any]) -> bool:
        """Add or update NFT item in database"""
        try:
            with self.get_session() as session:
                existing_item = session.query(NFTItem).filter(
                    NFTItem.definition_id == str(nft_data.get("definition_id"))
                ).first()
                
                if existing_item:
                    # Update existing item
                    existing_item.price_usd = nft_data.get("price_usd")
                    existing_item.price_native = nft_data.get("price_native")
                    existing_item.preview_url = nft_data.get("preview_url")
                    existing_item.attributes = nft_data.get("attributes")
                    existing_item.is_available = True
                    logger.info(f"Updated NFT item: {existing_item.name}")
                else:
                    # Create new item
                    new_item = NFTItem(
                        definition_id=str(nft_data.get("definition_id")),
                        token_id=str(nft_data.get("token_id")),
                        collection_slug=nft_data.get("collection_slug"),
                        name=nft_data.get("name"),
                        price_usd=nft_data.get("price_usd"),
                        price_native=nft_data.get("price_native"),
                        contract_hash=nft_data.get("contract_hash"),
                        preview_url=nft_data.get("preview_url"),
                        external_link=nft_data.get("external_link"),
                        attributes=nft_data.get("attributes")
                    )
                    session.add(new_item)
                    logger.info(f"Added new NFT item: {new_item.name}")
                
                session.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error adding/updating NFT item: {e}")
            return False
    
    def get_cheapest_nft_by_collection(self, collection_slug: str) -> Optional[NFTItem]:
        """Get cheapest available NFT from collection"""
        try:
            with self.get_session() as session:
                item = session.query(NFTItem).filter(
                    NFTItem.collection_slug == collection_slug,
                    NFTItem.is_available == True
                ).order_by(NFTItem.price_usd.asc()).first()
                
                return item
                
        except Exception as e:
            logger.error(f"Error fetching cheapest NFT: {e}")
            return None
    
    def add_user_gift(self, user_id: int, received_gift_id: str, definition_id: str, gift_name: str = None) -> bool:
        """Add received gift to user's inventory"""
        try:
            with self.get_session() as session:
                user_gift = UserGift(
                    user_id=user_id,
                    received_gift_id=received_gift_id,
                    definition_id=definition_id,
                    gift_name=gift_name
                )
                session.add(user_gift)
                session.commit()
                logger.info(f"Added gift {received_gift_id} to user {user_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error adding user gift: {e}")
            return False
    
    def get_available_user_gift(self, user_id: int, definition_id: str = None) -> Optional[UserGift]:
        """Get available gift from user's inventory"""
        try:
            with self.get_session() as session:
                query = session.query(UserGift).filter(
                    UserGift.user_id == user_id,
                    UserGift.is_used == False
                )
                
                if definition_id:
                    query = query.filter(UserGift.definition_id == definition_id)
                
                gift = query.first()
                return gift
                
        except Exception as e:
            logger.error(f"Error fetching user gift: {e}")
            return None
    
    def mark_gift_as_used(self, gift_id: int) -> bool:
        """Mark gift as used"""
        try:
            with self.get_session() as session:
                gift = session.query(UserGift).filter(UserGift.id == gift_id).first()
                if gift:
                    gift.is_used = True
                    gift.used_at = datetime.now()
                    session.commit()
                    logger.info(f"Marked gift {gift_id} as used")
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Error marking gift as used: {e}")
            return False


# Global database manager instance
db_manager = DatabaseManager()
