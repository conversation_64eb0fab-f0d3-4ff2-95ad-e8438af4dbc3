# NFT Casino Bot

Telegram бот для NFT-казино с интеграцией Fragment API и MTProto для работы с подарками.

## Возможности

- 🎰 **NFT Казино** - Игры с NFT-ставками
- 💎 **Fragment API** - Получение данных о коллекциях NFT
- 🎁 **MTProto Integration** - Покупка и отправка Telegram подарков
- 📊 **Мониторинг цен** - Отслеживание минимальных цен на Fragment
- 🗄️ **База данных** - Хранение данных о NFT и пользователях

## Структура проекта

```
NFT BOT/
├── main.py                 # Главный файл запуска
├── nft_bot.py             # Telegram бот
├── fragment_api.py        # Клиент Fragment API
├── mtproto_client.py      # MTProto клиент для подарков
├── database.py            # Модели и операции БД
├── config.py              # Конфигурация
├── test_fragment_api.py   # Тесты
├── requirements.txt       # Зависимости
├── .env.example          # Пример конфигурации
└── README.md             # Документация
```

## Установка

1. **Клонируйте репозиторий:**
```bash
git clone <repository-url>
cd "NFT BOT"
```

2. **Установите зависимости:**
```bash
pip install -r requirements.txt
```

3. **Настройте конфигурацию:**
```bash
cp .env.example .env
```

Отредактируйте `.env` файл:
```env
BOT_TOKEN=your_bot_token_here
API_ID=your_api_id_here
API_HASH=your_api_hash_here
SESSION_NAME=nft_bot_session
DATABASE_URL=sqlite:///nft_bot.db
ADMIN_USER_ID=your_admin_user_id
```

## Получение API ключей

### Telegram Bot Token
1. Создайте бота через [@BotFather](https://t.me/BotFather)
2. Получите токен и добавьте в `BOT_TOKEN`

### Telegram API ID/Hash
1. Зайдите на [my.telegram.org](https://my.telegram.org)
2. Создайте приложение
3. Получите `API_ID` и `API_HASH`

## Запуск

### Полный запуск (бот + MTProto):
```bash
python main.py
```

### Только Telegram бот:
```bash
python main.py bot
```

### Только MTProto клиент:
```bash
python main.py mtproto
```

### Тесты:
```bash
python main.py test
```

## Fragment API

Бот использует Fragment API для получения данных о NFT коллекциях:

### Основные функции:
- `fetch_cheapest_nft(collection_slug)` - Получить самый дешевый NFT
- `fetch_collection_lots(collection_slug, limit, page)` - Получить список лотов
- `get_collection_stats(collection_slug)` - Статистика коллекции

### Пример использования:
```python
from fragment_api import fragment_client

# Получить самый дешевый Astral Shard
nft_data = fragment_client.fetch_cheapest_nft("astralshard")
print(f"Цена: ${nft_data['price_usd']}")
print(f"Definition ID: {nft_data['definition_id']}")
```

## MTProto Integration

Для работы с Telegram подарками используется MTProto:

### Основные функции:
- `create_gift_invoice()` - Создать счет для покупки подарка
- `send_gift_to_user()` - Отправить подарок пользователю
- `handle_gift_received()` - Обработать полученный подарок

### Пример покупки NFT:
```python
# Создать счет для покупки
invoice_data = await client.create_gift_invoice(
    user_id=user_id,
    user_access_hash=access_hash,
    gift_definition_id="9876543210987654321",
    message="NFT Casino Purchase"
)

# Отправить подарок победителю
await client.send_gift_to_user(
    user_id=winner_id,
    user_access_hash=winner_hash,
    received_gift_id=gift_id,
    message="Поздравляем с выигрышем!"
)
```

## База данных

Используется SQLAlchemy с SQLite:

### Модели:
- `NFTCollection` - Коллекции NFT
- `NFTItem` - Отдельные NFT предметы
- `UserGift` - Подарки пользователей
- `GameSession` - Игровые сессии

### Операции:
```python
from database import db_manager

# Добавить NFT в БД
db_manager.add_or_update_nft_item(nft_data)

# Получить самый дешевый NFT
nft = db_manager.get_cheapest_nft_by_collection("astralshard")

# Добавить подарок пользователю
db_manager.add_user_gift(user_id, gift_id, definition_id)
```

## Команды бота

- `/start` - Главное меню
- `/collections` - Просмотр коллекций NFT
- `/casino` - Меню казино
- `/inventory` - Инвентарь пользователя
- `/refresh` - Обновить данные (только админ)

## Логирование

Логи сохраняются в файл `nft_bot.log` с ротацией:
- Максимальный размер: 10 MB
- Хранение: 7 дней
- Уровень: INFO (настраивается в .env)

## Разработка

### Тестирование Fragment API:
```bash
python test_fragment_api.py
```

### Структура тестов:
1. Получение данных из Fragment API
2. Сохранение в базу данных
3. Извлечение из базы данных
4. Операции с подарками пользователей

## Безопасность

⚠️ **Важно:**
- Никогда не коммитьте `.env` файл
- Используйте сильные пароли для БД
- Ограничьте доступ к admin функциям
- Регулярно обновляйте зависимости

## Лицензия

MIT License

## Поддержка

Для вопросов и предложений создавайте Issues в репозитории.

---

**Примечание:** Это демо-версия. В продакшене требуется дополнительная настройка безопасности и обработки ошибок.
