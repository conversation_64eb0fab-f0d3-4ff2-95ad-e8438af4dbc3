"""
Configuration module for NFT Bot
"""
import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

load_dotenv()


class Settings(BaseSettings):
    """Application settings"""
    
    # Telegram Bot Configuration
    bot_token: str = Field(..., env="BOT_TOKEN")
    api_id: int = Field(..., env="API_ID")
    api_hash: str = Field(..., env="API_HASH")
    session_name: str = Field(default="nft_bot_session", env="SESSION_NAME")
    
    # Database Configuration
    database_url: str = Field(default="sqlite:///nft_bot.db", env="DATABASE_URL")
    
    # Fragment API Configuration
    fragment_base_url: str = Field(
        default="https://fragment.com/api/v1", 
        env="FRAGMENT_BASE_URL"
    )
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="nft_bot.log", env="LOG_FILE")
    
    # Bot Configuration
    admin_user_id: Optional[int] = Field(default=None, env="ADMIN_USER_ID")
    casino_wallet_address: Optional[str] = Field(default=None, env="CASINO_WALLET_ADDRESS")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
