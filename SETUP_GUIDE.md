# NFT Casino Bot - Setup Guide

## 🎯 Обзор проекта

NFT Casino Bot - это Telegram бот для NFT-казино с интеграцией Fragment API. Бот позволяет:

- 🎰 Играть в казино с NFT-ставками
- 💎 Отслеживать цены NFT на Fragment
- 🎁 Покупать и отправлять Telegram подарки через MTProto
- 📊 Управлять инвентарем NFT

## 📁 Структура проекта

```
NFT BOT/
├── main.py                 # Главный файл запуска
├── nft_bot.py             # Telegram бот
├── fragment_api.py        # Fragment API клиент
├── mtproto_client.py      # MTProto для подарков
├── database.py            # База данных
├── config.py              # Конфигурация
├── demo_bot.py            # Демонстрация функций
├── test_fragment_api.py   # Тесты
├── check_collections.py   # Проверка коллекций
├── requirements.txt       # Зависимости
├── .env                   # Конфигурация (создать)
├── .env.example          # Пример конфигурации
└── README.md             # Документация
```

## 🚀 Быстрый старт

### 1. Установка зависимостей

```bash
pip install -r requirements.txt
```

### 2. Настройка конфигурации

Скопируйте `.env.example` в `.env` и заполните:

```env
# Telegram Bot Token (получить у @BotFather)
BOT_TOKEN=your_bot_token_here

# Telegram API (получить на my.telegram.org)
API_ID=your_api_id_here
API_HASH=your_api_hash_here

# Остальные настройки
SESSION_NAME=nft_bot_session
DATABASE_URL=sqlite:///nft_bot.db
ADMIN_USER_ID=your_telegram_user_id
```

### 3. Демонстрация

Запустите демо без настройки токенов:

```bash
python demo_bot.py
```

### 4. Тестирование

```bash
python test_fragment_api.py
```

### 5. Запуск бота

```bash
# Полный запуск (бот + MTProto)
python main.py

# Только бот
python main.py bot

# Только MTProto клиент
python main.py mtproto
```

## 🔧 Подробная настройка

### Получение Telegram Bot Token

1. Напишите [@BotFather](https://t.me/BotFather)
2. Создайте нового бота: `/newbot`
3. Выберите имя и username
4. Получите токен и добавьте в `BOT_TOKEN`

### Получение Telegram API ключей

1. Зайдите на [my.telegram.org](https://my.telegram.org)
2. Войдите с вашим номером телефона
3. Перейдите в "API development tools"
4. Создайте новое приложение
5. Получите `API_ID` и `API_HASH`

### Настройка базы данных

По умолчанию используется SQLite. Для PostgreSQL:

```env
DATABASE_URL=postgresql://user:password@localhost/nft_bot
```

## 📊 Fragment API

### Поддерживаемые коллекции

- `astralshard` - Astral Shard NFTs
- `telegram-numbers` - Telegram номера
- `telegram-usernames` - Telegram юзернеймы

### Пример использования

```python
from fragment_api import fragment_client

# Получить самый дешевый NFT
nft_data = fragment_client.fetch_cheapest_nft("astralshard")
print(f"Цена: ${nft_data['price_usd']}")
```

## 🎮 Игровая механика

### Поддерживаемые игры

1. **Dice Game** - Угадать число (1-6)
2. **Card Game** - Больше/меньше
3. **Roulette** - Красное/черное

### Логика выплат

- Шанс выигрыша: 45%
- Выплата при выигрыше: 2x ставка
- Ставки принимаются в NFT

## 🔐 Безопасность

### Важные моменты

- ❌ Никогда не коммитьте `.env` файл
- ✅ Используйте сильные пароли
- ✅ Ограничьте admin доступ
- ✅ Регулярно обновляйте зависимости

### Рекомендации для продакшена

```env
# Используйте PostgreSQL
DATABASE_URL=postgresql://...

# Настройте логирование
LOG_LEVEL=WARNING
LOG_FILE=/var/log/nft_bot.log

# Ограничьте admin доступ
ADMIN_USER_ID=your_real_user_id
```

## 🧪 Тестирование

### Запуск всех тестов

```bash
python main.py test
```

### Проверка коллекций

```bash
python check_collections.py
```

### Демонстрация функций

```bash
python demo_bot.py
```

## 🚀 Деплой

### Docker (рекомендуется)

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "main.py"]
```

### Systemd Service

```ini
[Unit]
Description=NFT Casino Bot
After=network.target

[Service]
Type=simple
User=nftbot
WorkingDirectory=/opt/nft-bot
ExecStart=/usr/bin/python3 main.py
Restart=always

[Install]
WantedBy=multi-user.target
```

## 📝 Команды бота

| Команда | Описание |
|---------|----------|
| `/start` | Главное меню |
| `/collections` | Просмотр NFT коллекций |
| `/casino` | Меню казино |
| `/inventory` | Инвентарь пользователя |
| `/refresh` | Обновить данные (admin) |

## 🐛 Устранение неполадок

### Частые проблемы

1. **"Bot token not configured"**
   - Проверьте `.env` файл
   - Убедитесь, что `BOT_TOKEN` заполнен

2. **"Fragment API error"**
   - Нормально для демо-версии
   - Используются мок-данные

3. **"Database error"**
   - Проверьте права доступа к файлу БД
   - Убедитесь, что SQLite установлен

### Логи

Логи сохраняются в `nft_bot.log`:

```bash
tail -f nft_bot.log
```

## 📞 Поддержка

- 📧 Email: <EMAIL>
- 💬 Telegram: @support_bot
- 🐛 Issues: GitHub Issues

## 📄 Лицензия

MIT License - см. LICENSE файл

---

**⚠️ Важно:** Это демо-версия для обучения. В продакшене требуется дополнительная настройка безопасности и обработки ошибок.
