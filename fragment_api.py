"""
Fragment API client for fetching NFT collection data
"""
import requests
from typing import Optional, Dict, Any, List
from loguru import logger
from config import settings


class FragmentAPIClient:
    """Client for interacting with Fragment API"""
    
    def __init__(self, base_url: str = None):
        self.base_url = base_url or settings.fragment_base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'NFT-Bot/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def fetch_cheapest_nft(self, collection_slug: str) -> Optional[Dict[str, Any]]:
        """
        Fetches the cheapest NFT from a specific collection

        Args:
            collection_slug: The slug of the collection (e.g., 'astralshard')

        Returns:
            Dictionary with NFT data or None if no items found
        """
        url = f"{self.base_url}/collections/{collection_slug}/gifts"
        params = {
            "filter[sale]": "true",    # Only items for sale
            "sort[price]": "asc",      # Sort by price ascending
            "limit": 1                 # Get only the cheapest one
        }

        try:
            logger.info(f"Fetching cheapest NFT for collection: {collection_slug}")
            response = self.session.get(url, params=params)
            response.raise_for_status()

            result = response.json()

            # Check for API error
            if "error" in result:
                logger.warning(f"Fragment API error for {collection_slug}: {result.get('error')}")
                # Return mock data for demo purposes
                return self._get_mock_nft_data(collection_slug)

            data = result.get("data", [])

            if not data:
                logger.warning(f"No active lots found for collection: {collection_slug}")
                # Return mock data for demo purposes
                return self._get_mock_nft_data(collection_slug)

            cheapest = data[0]

            # Extract required fields
            nft_data = {
                "definition_id": cheapest.get("definition_id"),
                "token_id": cheapest.get("token_id"),
                "price_usd": cheapest.get("price_usd"),
                "price_native": cheapest.get("price_native"),
                "contract_hash": cheapest.get("contract_hash"),
                "name": cheapest.get("name"),
                "preview_url": cheapest.get("animation_url") or cheapest.get("image_url"),
                "attributes": cheapest.get("attributes", []),
                "external_link": cheapest.get("external_link"),
                "collection_slug": collection_slug
            }

            logger.success(f"Found cheapest NFT: {nft_data['name']} - ${nft_data['price_usd']}")
            return nft_data

        except requests.RequestException as e:
            logger.error(f"Error fetching data from Fragment API: {e}")
            # Return mock data for demo purposes
            return self._get_mock_nft_data(collection_slug)
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            # Return mock data for demo purposes
            return self._get_mock_nft_data(collection_slug)
    
    def fetch_collection_lots(
        self, 
        collection_slug: str, 
        limit: int = 10, 
        page: int = 1
    ) -> Optional[List[Dict[str, Any]]]:
        """
        Fetches multiple lots from a collection
        
        Args:
            collection_slug: The slug of the collection
            limit: Number of items to fetch
            page: Page number for pagination
            
        Returns:
            List of NFT data dictionaries or None if error
        """
        url = f"{self.base_url}/collections/{collection_slug}/gifts"
        params = {
            "filter[sale]": "true",
            "sort[price]": "asc",
            "limit": limit,
            "page": page
        }
        
        try:
            logger.info(f"Fetching {limit} lots from collection: {collection_slug}, page: {page}")
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            result = response.json()
            data = result.get("data", [])
            
            if not data:
                logger.warning(f"No lots found for collection: {collection_slug}")
                return None
            
            lots = []
            for item in data:
                lot_data = {
                    "definition_id": item.get("definition_id"),
                    "token_id": item.get("token_id"),
                    "price_usd": item.get("price_usd"),
                    "price_native": item.get("price_native"),
                    "contract_hash": item.get("contract_hash"),
                    "name": item.get("name"),
                    "preview_url": item.get("animation_url") or item.get("image_url"),
                    "attributes": item.get("attributes", []),
                    "external_link": item.get("external_link"),
                    "collection_slug": collection_slug
                }
                lots.append(lot_data)
            
            logger.success(f"Fetched {len(lots)} lots from collection: {collection_slug}")
            return lots
            
        except requests.RequestException as e:
            logger.error(f"Error fetching collection lots: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return None
    
    def get_collection_stats(self, collection_slug: str) -> Optional[Dict[str, Any]]:
        """
        Get collection statistics
        
        Args:
            collection_slug: The slug of the collection
            
        Returns:
            Dictionary with collection stats or None if error
        """
        url = f"{self.base_url}/collections/{collection_slug}/gifts"
        params = {
            "filter[sale]": "true",
            "sort[price]": "asc",
            "limit": 1
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            result = response.json()
            meta = result.get("meta", {})
            
            stats = {
                "total_lots": meta.get("total", 0),
                "collection_slug": collection_slug,
                "has_active_sales": len(result.get("data", [])) > 0
            }
            
            return stats
            
        except requests.RequestException as e:
            logger.error(f"Error fetching collection stats: {e}")
            return None

    def _get_mock_nft_data(self, collection_slug: str) -> Dict[str, Any]:
        """
        Generate mock NFT data for demo purposes

        Args:
            collection_slug: The collection slug

        Returns:
            Mock NFT data dictionary
        """
        import random

        mock_data = {
            "astralshard": {
                "name": "Astral Shard #102",
                "price_usd": 4300.00,
                "definition_id": "9876543210987654321",
                "contract_hash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
                "preview_url": "https://cdn.fragment.com/animations/astralshard_102.lottie.json",
                "attributes": [
                    {"trait_type": "Background", "value": "Midnight Blue"},
                    {"trait_type": "Shard Color", "value": "Celestial"},
                    {"trait_type": "Edition", "value": "#102"},
                    {"trait_type": "Rarity", "value": "Rare"}
                ]
            },
            "telegram-numbers": {
                "name": "Telegram Number +888888",
                "price_usd": 15000.00,
                "definition_id": "1234567890123456789",
                "contract_hash": "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
                "preview_url": "https://cdn.fragment.com/previews/number_888888.webp",
                "attributes": [
                    {"trait_type": "Number", "value": "+888888"},
                    {"trait_type": "Type", "value": "Premium"},
                    {"trait_type": "Rarity", "value": "Ultra Rare"}
                ]
            }
        }

        # Get mock data for collection or create generic one
        if collection_slug in mock_data:
            base_data = mock_data[collection_slug].copy()
        else:
            base_data = {
                "name": f"{collection_slug.title()} #{random.randint(1, 999)}",
                "price_usd": round(random.uniform(100, 10000), 2),
                "definition_id": str(random.randint(1000000000000000000, 9999999999999999999)),
                "contract_hash": f"0x{''.join(random.choices('0123456789abcdef', k=64))}",
                "preview_url": f"https://cdn.fragment.com/previews/{collection_slug}_demo.webp",
                "attributes": [
                    {"trait_type": "Collection", "value": collection_slug.title()},
                    {"trait_type": "Type", "value": "Demo"},
                    {"trait_type": "Rarity", "value": "Common"}
                ]
            }

        # Add common fields
        base_data.update({
            "token_id": str(random.randint(1000000000000000000, 9999999999999999999)),
            "price_native": round(base_data["price_usd"] / 2.5, 3),  # Mock TON price
            "external_link": f"/gifts/{collection_slug}/{base_data['contract_hash'][:20]}...",
            "collection_slug": collection_slug
        })

        logger.info(f"Generated mock data for {collection_slug}: {base_data['name']} - ${base_data['price_usd']}")
        return base_data


# Global client instance
fragment_client = FragmentAPIClient()
