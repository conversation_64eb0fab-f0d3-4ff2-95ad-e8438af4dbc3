"""
Script to check available collections on Fragment
"""
import requests
from fragment_api import fragment_client


def check_popular_collections():
    """Check some popular NFT collections"""
    
    # List of popular collections to test
    collections = [
        "astralshard",
        "telegram-numbers", 
        "telegram-usernames",
        "anonymous-numbers",
        "fragment-numbers"
    ]
    
    print("🔍 Checking popular Fragment collections...\n")
    
    for collection in collections:
        print(f"📦 Collection: {collection}")
        
        # Get collection stats
        stats = fragment_client.get_collection_stats(collection)
        if stats:
            print(f"   Total lots: {stats['total_lots']}")
            print(f"   Has active sales: {stats['has_active_sales']}")
            
            if stats['has_active_sales']:
                # Get cheapest item
                nft_data = fragment_client.fetch_cheapest_nft(collection)
                if nft_data:
                    print(f"   ✅ Cheapest item: {nft_data['name']}")
                    print(f"   💰 Price: ${nft_data['price_usd']:,.2f}")
                    print(f"   🆔 Definition ID: {nft_data['definition_id']}")
                else:
                    print("   ❌ Could not fetch cheapest item")
            else:
                print("   ⚠️ No active sales")
        else:
            print("   ❌ Collection not found or error")
        
        print()


def test_direct_api_call():
    """Test direct API call to Fragment"""
    print("🌐 Testing direct Fragment API call...\n")
    
    url = "https://fragment.com/api/v1/collections/telegram-numbers/gifts"
    params = {
        "filter[sale]": "true",
        "sort[price]": "asc",
        "limit": 1
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response keys: {list(data.keys())}")
            
            if 'data' in data and data['data']:
                item = data['data'][0]
                print(f"✅ Found item: {item.get('name', 'Unknown')}")
                print(f"💰 Price: ${item.get('price_usd', 0):,.2f}")
            else:
                print("⚠️ No items in response")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    check_popular_collections()
    test_direct_api_call()
