"""
Test script for Fragment API functionality
"""
import asyncio
from fragment_api import fragment_client
from database import db_manager
from loguru import logger


def test_fragment_api():
    """Test Fragment API functionality"""
    print("🔍 Testing Fragment API...")
    
    # Test 1: Fetch cheapest NFT from astralshard collection
    print("\n1. Fetching cheapest Astral Shard NFT...")
    nft_data = fragment_client.fetch_cheapest_nft("astralshard")
    
    if nft_data:
        print("✅ Success! Found NFT:")
        print(f"   Name: {nft_data['name']}")
        print(f"   Price (USD): ${nft_data['price_usd']:,.2f}")
        print(f"   Definition ID: {nft_data['definition_id']}")
        print(f"   Contract Hash: {nft_data['contract_hash']}")
        print(f"   Preview URL: {nft_data['preview_url']}")
        
        # Test database storage
        print("\n2. Testing database storage...")
        success = db_manager.add_or_update_nft_item(nft_data)
        if success:
            print("✅ NFT data saved to database successfully!")
        else:
            print("❌ Failed to save NFT data to database")
        
        # Test database retrieval
        print("\n3. Testing database retrieval...")
        stored_nft = db_manager.get_cheapest_nft_by_collection("astralshard")
        if stored_nft:
            print("✅ NFT data retrieved from database:")
            print(f"   Name: {stored_nft.name}")
            print(f"   Price (USD): ${stored_nft.price_usd:,.2f}")
            print(f"   Definition ID: {stored_nft.definition_id}")
        else:
            print("❌ Failed to retrieve NFT data from database")
    else:
        print("❌ Failed to fetch NFT data from Fragment API")
    
    # Test 2: Fetch multiple lots
    print("\n4. Fetching multiple lots...")
    lots = fragment_client.fetch_collection_lots("astralshard", limit=5)
    
    if lots:
        print(f"✅ Found {len(lots)} lots:")
        for i, lot in enumerate(lots, 1):
            print(f"   {i}. {lot['name']} - ${lot['price_usd']:,.2f}")
    else:
        print("❌ Failed to fetch multiple lots")
    
    # Test 3: Get collection stats
    print("\n5. Getting collection statistics...")
    stats = fragment_client.get_collection_stats("astralshard")
    
    if stats:
        print("✅ Collection stats:")
        print(f"   Total lots: {stats['total_lots']}")
        print(f"   Has active sales: {stats['has_active_sales']}")
    else:
        print("❌ Failed to get collection stats")


def test_database_operations():
    """Test database operations"""
    print("\n🗄️ Testing database operations...")
    
    # Test user gift operations
    print("\n1. Testing user gift operations...")
    
    # Add a test gift
    success = db_manager.add_user_gift(
        user_id=123456789,
        received_gift_id="test_gift_123",
        definition_id="9876543210987654321",
        gift_name="Test Astral Shard"
    )
    
    if success:
        print("✅ Test gift added successfully")
        
        # Retrieve the gift
        gift = db_manager.get_available_user_gift(123456789)
        if gift:
            print(f"✅ Gift retrieved: {gift.gift_name}")
            
            # Mark as used
            used = db_manager.mark_gift_as_used(gift.id)
            if used:
                print("✅ Gift marked as used")
            else:
                print("❌ Failed to mark gift as used")
        else:
            print("❌ Failed to retrieve gift")
    else:
        print("❌ Failed to add test gift")


def main():
    """Main test function"""
    print("🚀 Starting NFT Bot tests...\n")
    
    # Configure logging
    logger.add("test.log", rotation="1 MB")
    
    try:
        # Test Fragment API
        test_fragment_api()
        
        # Test database operations
        test_database_operations()
        
        print("\n✅ All tests completed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        logger.error(f"Test failed: {e}")


if __name__ == "__main__":
    main()
