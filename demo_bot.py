"""
Demo script to showcase NFT Bot functionality without requiring real Telegram tokens
"""
from fragment_api import fragment_client
from database import db_manager
from loguru import logger
import asyncio


class NFTBotDemo:
    """Demo class to showcase NFT Bot functionality"""
    
    def __init__(self):
        self.collections = ["astralshard", "telegram-numbers", "cosmic-gems"]
    
    def demo_fragment_api(self):
        """Demonstrate Fragment API functionality"""
        print("🔍 Fragment API Demo")
        print("=" * 50)
        
        for collection in self.collections:
            print(f"\n📦 Collection: {collection}")
            
            # Fetch cheapest NFT
            nft_data = fragment_client.fetch_cheapest_nft(collection)
            
            if nft_data:
                print(f"✅ Found: {nft_data['name']}")
                print(f"💰 Price: ${nft_data['price_usd']:,.2f}")
                print(f"🆔 Definition ID: {nft_data['definition_id']}")
                print(f"🔗 Contract: {nft_data['contract_hash'][:20]}...")
                print(f"🎨 Preview: {nft_data['preview_url']}")
                
                # Save to database
                success = db_manager.add_or_update_nft_item(nft_data)
                if success:
                    print("✅ Saved to database")
                else:
                    print("❌ Failed to save to database")
            else:
                print("❌ No data found")
    
    def demo_database_operations(self):
        """Demonstrate database operations"""
        print("\n\n🗄️ Database Operations Demo")
        print("=" * 50)
        
        # Test user gift operations
        print("\n1. Adding user gifts...")
        
        test_users = [
            {"user_id": 123456789, "gift_name": "Astral Shard #102"},
            {"user_id": 987654321, "gift_name": "Cosmic Gem #45"},
            {"user_id": 555666777, "gift_name": "Telegram Number +888888"}
        ]
        
        for user in test_users:
            success = db_manager.add_user_gift(
                user_id=user["user_id"],
                received_gift_id=f"gift_{user['user_id']}",
                definition_id="9876543210987654321",
                gift_name=user["gift_name"]
            )
            
            if success:
                print(f"✅ Added gift for user {user['user_id']}: {user['gift_name']}")
            else:
                print(f"❌ Failed to add gift for user {user['user_id']}")
        
        # Test retrieving gifts
        print("\n2. Retrieving user gifts...")
        
        for user in test_users:
            gift = db_manager.get_available_user_gift(user["user_id"])
            if gift:
                print(f"✅ User {user['user_id']} has gift: {gift.gift_name}")
                
                # Mark as used
                used = db_manager.mark_gift_as_used(gift.id)
                if used:
                    print(f"   ✅ Gift marked as used")
                else:
                    print(f"   ❌ Failed to mark gift as used")
            else:
                print(f"❌ No gifts found for user {user['user_id']}")
        
        # Test NFT retrieval
        print("\n3. Retrieving NFTs from database...")
        
        for collection in self.collections:
            nft = db_manager.get_cheapest_nft_by_collection(collection)
            if nft:
                print(f"✅ {collection}: {nft.name} - ${nft.price_usd:,.2f}")
            else:
                print(f"❌ No NFTs found for {collection}")
    
    def demo_casino_logic(self):
        """Demonstrate casino game logic"""
        print("\n\n🎰 Casino Logic Demo")
        print("=" * 50)
        
        import random
        
        # Simulate casino games
        games = [
            {"name": "Dice Game", "bet": 4300.00, "nft": "Astral Shard #102"},
            {"name": "Card Game", "bet": 15000.00, "nft": "Telegram Number +888888"},
            {"name": "Roulette", "bet": 2500.00, "nft": "Cosmic Gem #45"}
        ]
        
        for game in games:
            print(f"\n🎮 {game['name']}")
            print(f"💰 Bet: ${game['bet']:,.2f} ({game['nft']})")
            
            # Simulate game result
            win_chance = 0.45  # 45% win chance
            is_winner = random.random() < win_chance
            
            if is_winner:
                payout = game['bet'] * 2  # 2x payout
                print(f"🎉 WIN! Payout: ${payout:,.2f}")
                print(f"🎁 Player receives: {game['nft']} (x2)")
            else:
                print(f"😔 LOSE! Better luck next time!")
                print(f"💸 Lost: {game['nft']}")
    
    def demo_mtproto_simulation(self):
        """Demonstrate MTProto operations (simulated)"""
        print("\n\n📱 MTProto Operations Demo (Simulated)")
        print("=" * 50)
        
        operations = [
            {
                "type": "create_invoice",
                "user_id": 123456789,
                "gift_id": "9876543210987654321",
                "amount": 4300.00
            },
            {
                "type": "send_gift",
                "user_id": 987654321,
                "gift_name": "Astral Shard #102",
                "message": "Congratulations on your casino win!"
            },
            {
                "type": "receive_gift",
                "from_user": 555666777,
                "gift_name": "Cosmic Gem #45",
                "value": 2500.00
            }
        ]
        
        for op in operations:
            if op["type"] == "create_invoice":
                print(f"\n💳 Creating invoice for user {op['user_id']}")
                print(f"   Gift ID: {op['gift_id']}")
                print(f"   Amount: ${op['amount']:,.2f}")
                print(f"   ✅ Invoice created successfully")
                
            elif op["type"] == "send_gift":
                print(f"\n🎁 Sending gift to user {op['user_id']}")
                print(f"   Gift: {op['gift_name']}")
                print(f"   Message: {op['message']}")
                print(f"   ✅ Gift sent successfully")
                
            elif op["type"] == "receive_gift":
                print(f"\n📥 Received gift from user {op['from_user']}")
                print(f"   Gift: {op['gift_name']}")
                print(f"   Value: ${op['value']:,.2f}")
                print(f"   ✅ Gift added to inventory")
    
    def demo_bot_commands(self):
        """Demonstrate bot command responses"""
        print("\n\n🤖 Bot Commands Demo")
        print("=" * 50)
        
        commands = [
            {"command": "/start", "description": "Welcome message with main menu"},
            {"command": "/collections", "description": "Show available NFT collections"},
            {"command": "/casino", "description": "Show casino games menu"},
            {"command": "/inventory", "description": "Show user's NFT inventory"},
            {"command": "/refresh", "description": "Update collection data (admin only)"}
        ]
        
        for cmd in commands:
            print(f"\n🔸 {cmd['command']}")
            print(f"   {cmd['description']}")
            
            if cmd['command'] == "/collections":
                print("   Response:")
                nft_data = fragment_client.fetch_cheapest_nft("astralshard")
                if nft_data:
                    print(f"   💎 Astral Shard Collection")
                    print(f"   🏷️ {nft_data['name']}")
                    print(f"   💰 ${nft_data['price_usd']:,.2f}")
                    print(f"   🆔 {nft_data['definition_id']}")
            
            elif cmd['command'] == "/casino":
                print("   Response:")
                print("   🎰 NFT Casino")
                print("   🎲 Dice Game")
                print("   🃏 Card Game") 
                print("   🎯 Roulette")
            
            elif cmd['command'] == "/inventory":
                print("   Response:")
                print("   🎁 Your NFT Inventory")
                print("   📦 No NFTs yet - play casino to win some!")
    
    def run_full_demo(self):
        """Run complete demonstration"""
        print("🚀 NFT Casino Bot - Full Demo")
        print("=" * 70)
        print("This demo showcases all the functionality of the NFT Casino Bot")
        print("without requiring real Telegram tokens or live API connections.")
        print("=" * 70)
        
        try:
            # Run all demos
            self.demo_fragment_api()
            self.demo_database_operations()
            self.demo_casino_logic()
            self.demo_mtproto_simulation()
            self.demo_bot_commands()
            
            print("\n\n✅ Demo completed successfully!")
            print("\n📋 Summary:")
            print("• Fragment API integration working (with mock data)")
            print("• Database operations functional")
            print("• Casino game logic implemented")
            print("• MTProto operations simulated")
            print("• Bot commands ready")
            print("\n🔧 Next steps:")
            print("• Configure real Telegram bot token")
            print("• Set up MTProto credentials")
            print("• Deploy to production server")
            print("• Test with real Fragment API endpoints")
            
        except Exception as e:
            logger.error(f"Demo failed: {e}")
            print(f"\n❌ Demo failed with error: {e}")


def main():
    """Main demo function"""
    demo = NFTBotDemo()
    demo.run_full_demo()


if __name__ == "__main__":
    main()
