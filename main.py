"""
Main entry point for NFT Bot
"""
import asyncio
import sys
from loguru import logger
from config import settings
from nft_bot import NFTBot
from mtproto_client import MTProtoClient
from database import db_manager
from fragment_api import fragment_client


async def setup_logging():
    """Setup logging configuration"""
    logger.remove()  # Remove default handler
    
    # Add console handler
    logger.add(
        sys.stdout,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # Add file handler
    logger.add(
        settings.log_file,
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="7 days"
    )
    
    logger.info("Logging configured successfully")


async def initialize_database():
    """Initialize database and create tables"""
    try:
        logger.info("Initializing database...")
        # Database is initialized in db_manager constructor
        logger.success("Database initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        return False


async def update_initial_data():
    """Update initial NFT collection data"""
    try:
        logger.info("Updating initial NFT collection data...")
        
        # Fetch and store astralshard collection data
        nft_data = fragment_client.fetch_cheapest_nft("astralshard")
        if nft_data:
            success = db_manager.add_or_update_nft_item(nft_data)
            if success:
                logger.success("Initial NFT data updated successfully")
            else:
                logger.warning("Failed to store NFT data in database")
        else:
            logger.warning("No NFT data found for astralshard collection")
        
        return True
    except Exception as e:
        logger.error(f"Failed to update initial data: {e}")
        return False


async def run_bot():
    """Run the main bot"""
    try:
        logger.info("Starting NFT Bot...")
        
        # Initialize bot
        bot = NFTBot()
        
        # Run bot
        await bot.run()
        
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot crashed: {e}")
        raise


async def run_mtproto_client():
    """Run MTProto client for handling gifts"""
    try:
        logger.info("Starting MTProto client...")
        
        client = MTProtoClient()
        await client.run_until_disconnected()
        
    except KeyboardInterrupt:
        logger.info("MTProto client stopped by user")
    except Exception as e:
        logger.error(f"MTProto client crashed: {e}")
        raise


async def main():
    """Main application entry point"""
    print("🤖 NFT Casino Bot")
    print("=" * 50)
    
    # Setup logging
    await setup_logging()
    
    # Check configuration
    if not settings.bot_token:
        logger.error("BOT_TOKEN not configured. Please check your .env file")
        return
    
    if not settings.api_id or not settings.api_hash:
        logger.error("API_ID and API_HASH not configured. Please check your .env file")
        return
    
    # Initialize database
    if not await initialize_database():
        logger.error("Failed to initialize database. Exiting...")
        return
    
    # Update initial data
    await update_initial_data()
    
    # Choose what to run
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == "bot":
            await run_bot()
        elif mode == "mtproto":
            await run_mtproto_client()
        elif mode == "test":
            logger.info("Running tests...")
            from test_fragment_api import main as test_main
            test_main()
        else:
            logger.error(f"Unknown mode: {mode}")
            print_usage()
    else:
        # Run both bot and mtproto client concurrently
        logger.info("Starting both bot and MTProto client...")
        
        try:
            await asyncio.gather(
                run_bot(),
                run_mtproto_client()
            )
        except KeyboardInterrupt:
            logger.info("Application stopped by user")
        except Exception as e:
            logger.error(f"Application crashed: {e}")


def print_usage():
    """Print usage information"""
    print("\nUsage:")
    print("  python main.py          - Run both bot and MTProto client")
    print("  python main.py bot      - Run only Telegram bot")
    print("  python main.py mtproto  - Run only MTProto client")
    print("  python main.py test     - Run tests")
    print("\nMake sure to configure your .env file before running!")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
