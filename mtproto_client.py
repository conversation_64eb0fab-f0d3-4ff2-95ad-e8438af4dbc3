"""
MTProto client for Telegram gift operations
"""
from telethon import TelegramClient, events, functions, types
from telethon.errors import Session<PERSON><PERSON>wordNeededError, FloodWaitError
from typing import Optional, Dict, Any
from loguru import logger
from config import settings
import asyncio


class MTProtoClient:
    """MTProto client for handling Telegram gifts"""
    
    def __init__(self):
        self.client = TelegramClient(
            settings.session_name,
            settings.api_id,
            settings.api_hash
        )
        self.is_connected = False
    
    async def connect(self):
        """Connect to Telegram"""
        try:
            await self.client.start()
            self.is_connected = True
            logger.info("MTProto client connected successfully")
            
            # Set up event handlers
            self.client.add_event_handler(self.handle_gift_received, events.NewMessage)
            
        except Exception as e:
            logger.error(f"Failed to connect MTProto client: {e}")
            self.is_connected = False
    
    async def disconnect(self):
        """Disconnect from Telegram"""
        if self.is_connected:
            await self.client.disconnect()
            self.is_connected = False
            logger.info("MTProto client disconnected")
    
    async def handle_gift_received(self, event):
        """<PERSON>le received gift messages"""
        try:
            if hasattr(event.message, 'action') and hasattr(event.message.action, 'gift'):
                # This is a gift message
                gift = event.message.action.gift
                sender_id = event.sender_id
                
                logger.info(f"Received gift from user {sender_id}: {gift}")
                
                # Here you would save the gift to database
                # db_manager.add_user_gift(sender_id, gift.id, gift.definition_id, gift.name)
                
        except Exception as e:
            logger.error(f"Error handling gift received: {e}")
    
    async def create_gift_invoice(
        self, 
        user_id: int, 
        user_access_hash: int,
        gift_definition_id: str,
        message: str = "NFT Casino Purchase"
    ) -> Optional[Dict[str, Any]]:
        """
        Create invoice for purchasing a gift
        
        Args:
            user_id: Target user ID
            user_access_hash: User access hash
            gift_definition_id: Gift definition ID from Fragment
            message: Message to include with the gift
            
        Returns:
            Invoice data or None if failed
        """
        try:
            if not self.is_connected:
                await self.connect()
            
            # Create invoice for star gift
            result = await self.client(functions.payments.CreateInvoiceRequest(
                invoice=types.InputInvoiceStarGift(
                    user_id=types.InputUser(user_id=user_id, access_hash=user_access_hash),
                    gift_id=int(gift_definition_id),
                    hide_name=False,
                    include_upgrade=True,  # Get NFT version
                    message=types.InputFormattedText(text=message)
                )
            ))
            
            logger.info(f"Created gift invoice for user {user_id}, gift {gift_definition_id}")
            return {
                "invoice_url": result.url,
                "invoice_id": result.id,
                "gift_definition_id": gift_definition_id,
                "user_id": user_id
            }
            
        except FloodWaitError as e:
            logger.warning(f"Flood wait error: {e.seconds} seconds")
            return None
        except Exception as e:
            logger.error(f"Error creating gift invoice: {e}")
            return None
    
    async def send_gift_to_user(
        self,
        user_id: int,
        user_access_hash: int,
        received_gift_id: str,
        message: str = "Congratulations! Your casino winnings!"
    ) -> bool:
        """
        Send a gift to user
        
        Args:
            user_id: Target user ID
            user_access_hash: User access hash
            received_gift_id: ID of the received gift to send
            message: Message to include
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.is_connected:
                await self.connect()
            
            # Send gift to user
            result = await self.client(functions.messages.SendGiftToUserRequest(
                user_id=types.InputUser(user_id=user_id, access_hash=user_access_hash),
                received_gift_id=received_gift_id,
                message=types.InputFormattedText(text=message)
            ))
            
            logger.info(f"Sent gift {received_gift_id} to user {user_id}")
            return True
            
        except FloodWaitError as e:
            logger.warning(f"Flood wait error: {e.seconds} seconds")
            return False
        except Exception as e:
            logger.error(f"Error sending gift: {e}")
            return False
    
    async def get_user_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Get user information including access hash
        
        Args:
            user_id: User ID
            
        Returns:
            User info dictionary or None
        """
        try:
            if not self.is_connected:
                await self.connect()
            
            # Get user entity
            user = await self.client.get_entity(user_id)
            
            return {
                "user_id": user.id,
                "access_hash": user.access_hash,
                "username": user.username,
                "first_name": user.first_name,
                "last_name": user.last_name
            }
            
        except Exception as e:
            logger.error(f"Error getting user info: {e}")
            return None
    
    async def check_gift_status(self, invoice_id: str) -> Optional[str]:
        """
        Check the status of a gift invoice
        
        Args:
            invoice_id: Invoice ID to check
            
        Returns:
            Status string or None
        """
        try:
            if not self.is_connected:
                await self.connect()
            
            # This would check the invoice status
            # Implementation depends on Telegram API
            logger.info(f"Checking status for invoice {invoice_id}")
            
            # Placeholder - actual implementation would query Telegram
            return "pending"
            
        except Exception as e:
            logger.error(f"Error checking gift status: {e}")
            return None
    
    async def get_available_gifts(self) -> Optional[list]:
        """
        Get list of available gift definitions
        
        Returns:
            List of available gifts or None
        """
        try:
            if not self.is_connected:
                await self.connect()
            
            # This would get available gift definitions from Telegram
            # Implementation depends on Telegram API
            logger.info("Fetching available gifts")
            
            # Placeholder - actual implementation would query Telegram
            return []
            
        except Exception as e:
            logger.error(f"Error getting available gifts: {e}")
            return None
    
    async def run_until_disconnected(self):
        """Keep the client running to handle events"""
        if not self.is_connected:
            await self.connect()
        
        logger.info("MTProto client is running and listening for events...")
        await self.client.run_until_disconnected()


# Example usage functions
async def example_buy_and_send_gift():
    """Example of buying and sending a gift"""
    client = MTProtoClient()
    
    try:
        await client.connect()
        
        # Example: Create invoice for buying a gift
        invoice_data = await client.create_gift_invoice(
            user_id=123456789,
            user_access_hash=987654321,
            gift_definition_id="9876543210987654321",
            message="Welcome to NFT Casino!"
        )
        
        if invoice_data:
            print(f"Invoice created: {invoice_data['invoice_url']}")
        
        # Example: Send a gift from inventory
        success = await client.send_gift_to_user(
            user_id=123456789,
            user_access_hash=987654321,
            received_gift_id="some_received_gift_id",
            message="Congratulations on your win!"
        )
        
        if success:
            print("Gift sent successfully!")
        
    finally:
        await client.disconnect()


if __name__ == "__main__":
    # Run example
    asyncio.run(example_buy_and_send_gift())
